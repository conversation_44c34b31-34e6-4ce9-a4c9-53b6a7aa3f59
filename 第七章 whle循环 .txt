幻灯片 1:
Python 程序设计
第七章 while循环
作者：冯子晋

幻灯片 2:
第 7 章 用户输入和 while 循环
7.1 input() 函数的工作原理
7.2 while 循环简介
7.3 使用 while 循环处理列表和字典
7.4 小结
7.5 课后作业

幻灯片 3:
显示向用户显示的提示（prompt）
等待用户输入

7.1 input() 函数
name = input("请输入你的名字：")
print(f"\n你好，{name}！欢迎来到编程课堂。")
运行结果
请输入你的名字：

幻灯片 4:
先看常见的分行追加


自赋值运算符
# 追加字符串
msg = "我们需要你的名字来填写课程反馈问卷"
msg = msg + "\n请输入你的名字："
# 追加值
number = 0
number = number + 1

幻灯片 5:
使用自赋值运算符简化：



number = 0
number += 1
自赋值运算符
# “+=” 翻译为 “自己追加”
msg = "我们需要你的名字来填写课程反馈问卷"
msg += "\n请输入你的名字："

幻灯片 6:
常用的自赋值运算符有：

只需记得它们其实相当于：
自赋值运算符
number += 1
number -= 1
number *= 1
number /= 1
number //= 1
number **= 1
...
number = number + 1
number = number - 1
number = number * 1
number = number / 1
number = number // 1
number = number ** 1
...

幻灯片 7:
把文本当作数值直接使用，会导致类型错误（TypeError）：
7.1.2 获取数值输入
age = input("How old are you? ")
print(age >= 18)
运行结果
How old are you? 21
Traceback (most recent call last):
  File "<stdin>", line 1, in <module>
TypeError: '>=' not supported between instances of 'str' and 'int'

幻灯片 8:
7.1.2 获取数值输入
int() 函数：将文本转换为数值
age = input("How old are you? ")
age = int(age) #把字符串变成数值
print(age >= 18)
运行结果
How old are you? 21
True


幻灯片 9:
7.1.3 求模运算符
求模运算符（%）：返回两个数相除后的余数。
>>> 4 % 3
1
>>> 5 % 3
2
>>> 6 % 3
0
>>> 7 % 3
1

幻灯片 10:
7.1.3 求模运算符
求模运算符（%）：将两个数相除并返回余数。
取模运算有很多妙用，比如可以用它来判断一个数是否是偶数：
>>> 4 % 3
1
>>> 5 % 3
2
>>> 6 % 3
0
>>> 7 % 3
1
>>> 4 % 2 == 0
True
>>> 5 % 2 == 0
False
>>> 7 % 2 == 0
False
>>> 0 % 2 == 0
True

幻灯片 11:
7.2.1使用 while 循环
for 循环用于对指定范围的元素进行同样的操作，
而 while 循环则是一直执行，直到条件不满足为止。
current_number = 1
while current_number < 3:
    print(current_number)
    current_number += 1
运行结果
1
2

代码 current_number += 1 
是 current_number = current_ number + 1 的简写


幻灯片 24:
7.2.2 使用条件表达式退出
prompt = "\n你想吃什么外卖？"
prompt += "\n输入 '不点了' 来结束。"
food = ""

while food != '不点了':
    food = input(prompt)
    print(f"好的，已添加：{food}")

幻灯片 25:
7.2.3 使用标志退出
prompt="\n你想和室友说点什么？"
prompt+="\n输入 '睡着了' 表示结束聊天。"
talking=True  # 活动状态标志
whiletalking:
    yousay=input(prompt)
    ifyousay=='睡着了':
        talking=False
    else:
        print(f"你说：{yousay}")

幻灯片 26:
7.2.4 使用 break 退出
break 关键字：立即退出循环

current_number = 0
while True:
    if current_number > 5:
        break
    current_number += 1
    print(current_number)

运行结果
1
2
3
4
5
6

幻灯片 27:
7.2.5.1 使用 continue 跳过循环
continue （继续）关键字：中途终止当前的循环，继续下一轮循环

current_number = 0
while True:
    current_number += 1
    if current_number % 2 == 0:
        continue #当current_number是偶数，跳过打印
    print(current_number)

运行结果
1
3
5
7
...
(奇数打印，
偶数不打印)

幻灯片 28:
7.2.5.2 课堂练习：同时使用continue与break
1.思考运行结果是什么。为什么？
current_number = 0
while True:
    if current_number > 5:
        break
    current_number += 1
    if current_number % 2 == 0:
        continue
    print(current_number)
运行结果
1
3
5

幻灯片 29:
7.2.6 记得避免死循环
x = 1
while x <= 5:
    print(x)
运行结果
1
1
1
...
按 Ctrl+C 来结束程序的运行
更重要是用 条件测试表达式、标志（变量）、break关键字

幻灯片 30:
7.2 什么是while循环
while 循环在条件满足前执行反复的操作。
使用条件表达式退出：通过设定条件触发循环停止。
使用标志退出（条件表达式是变量）：利用标志变量调整控制流程，实现条件满足时停止。
使用break退出：立即终止循环。
使用continue跳过循环：跳过当前循环中的剩余代码，进入下次循环

幻灯片 31:
7.3 while 循环使用案例一
student_list = ['xiaoming', 'xiaohong', 'xiaoli']
in_class = []

while student_list:
    student = daqiandao.pop()
    print(f"{student.title()} 正在签到...")
    in_class.append(student)

print("\n以下同学已完成签到：")
for student in in_class:
    print(student.title())

幻灯片 32:
7.3.2 while 循环使用案例二
列表的 remove() 方法仅能移除首个匹配，
 用while 删除列表中的全部匹配：
运行结果
['dog', 'cat', 'dog', 'cat', 'rabbit']
['dog', 'dog'', 'rabbit']
pets = ['dog', 'cat', 'dog', 'cat', 'rabbit']
print(pets)

while 'cat' in pets:
    pets.remove('cat')

print(pets)

幻灯片 33:
7.3.2 while循环使用案例三
记录每个人毕业旅行想去哪里
votes = {}

while True:
    name = input("\n请输入你的名字：")
    place = input("你最想毕业旅行去哪里？")
    votes[name] = place

    cont = input("还有人要投票吗？(yes/no)：")
    if cont == 'no':
        break

print("\n投票结果如下：")
for name, place in votes.items():
    print(f"{name} 想去 {place}")

幻灯片 34:
7.4 小结
 input() 让用户提供信息，并根据需要转为数值
 while 循环
控制 while 循环流程的方式
结合 while 循环来操作列表和字典



幻灯片 35:
7.5 课后作业


1.下周一前提交超星学习通第七章作业。
2.预习第八章，思考哪些知识是重点，哪些知识是难点。
