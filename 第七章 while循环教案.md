# Python 程序设计课程教学设计方案

## 基本信息

- **课程名称**：Python 程序设计
- **授课章节**：第七章 用户输入和 while 循环
- **授课班级**：计算机科学与技术专业
- **授课时间**：90 分钟（2 学时）
- **授课教师**：冯子晋
- **授课地点**：计算机实验室

## 教学分析

### 学情分析

学生已掌握 Python 基础语法、列表、字典、for 循环等知识，具备一定的编程基础。但对用户交互和不确定次数循环的理解还需要加强，特别是循环控制逻辑的设计能力有待提升。

### 教材分析

本章是 Python 程序设计中的重要内容，while 循环作为程序控制结构的重要组成部分，在处理用户交互、数据处理等方面应用广泛。内容包括用户输入处理、while 循环语法、循环控制和实际应用。

## 教学目标

### 知识目标

1. 掌握 input()函数的使用方法和数据类型转换
2. 理解 while 循环的工作原理和执行流程
3. 掌握 break、continue 等循环控制语句的使用
4. 了解自赋值运算符的概念和应用

### 能力目标

1. 能够设计和实现用户交互程序
2. 能够合理使用 while 循环解决实际问题
3. 能够避免死循环，设计安全的循环结构
4. 能够结合 while 循环处理列表和字典数据

### 素质目标

1. 培养学生的逻辑思维能力和程序设计思维
2. 提高学生的用户体验意识和交互设计能力
3. 培养学生良好的编程习惯和调试能力
4. 增强学生解决复杂问题的信心和能力

## 教学重点与难点

### 教学重点

1. input()函数的使用和数据类型转换技巧
2. while 循环的基本语法和执行机制
3. break 和 continue 语句的正确使用
4. while 循环与数据结构的结合应用

### 教学难点

1. 循环条件的设计和死循环的避免
2. 复杂交互逻辑的程序设计
3. 循环中的异常处理和边界条件
4. while 循环在实际项目中的合理应用

## 教学方法与手段

### 教学方法

1. **讲授法**：系统讲解 while 循环的概念、语法和执行机制
2. **演示法**：通过实际代码演示展示循环的执行过程和控制方法
3. **案例教学法**：通过实际应用案例帮助学生理解 while 循环的使用场景
4. **实践教学法**：通过编程练习让学生动手实践，掌握循环控制技巧
5. **对比教学法**：对比 for 循环和 while 循环的特点和适用场景

### 教学手段

1. **多媒体课件**：使用 PPT 展示概念、语法和执行流程
2. **编程环境**：使用 Python IDE 进行实时代码演示和调试
3. **互动平台**：利用课堂互动系统进行实时问答和反馈
4. **在线资源**：结合超星学习通平台进行课后练习和拓展

## 教学过程设计

### 第一环节：课程导入（8 分钟）

#### 教学内容

**问题情境创设**：如何让程序与用户进行交互？如何处理不确定次数的重复操作？

#### 教学活动

1. **复习回顾**（3 分钟）

   - 回顾 for 循环的特点：适用于确定次数的循环
   - 提问：如果不知道循环次数怎么办？

2. **问题引入**（5 分钟）
   - 展示实际场景：ATM 取款、用户登录验证、菜单选择
   - 分析问题：这些场景的共同特点是什么？
   - 引出 while 循环：适用于不确定次数的循环控制

#### 设计意图

通过实际应用场景激发学生学习兴趣，建立 while 循环的必要性认知。

### 第二环节：基础知识学习（25 分钟）

#### 教学内容一：input()函数的工作原理（12 分钟）

**知识点**：用户输入处理和数据类型转换

**教学活动**：

1. **基本用法演示**（4 分钟）

   ```python
   name = input("请输入你的名字：")
   print(f"\n你好，{name}！欢迎来到编程课堂。")
   ```

2. **自赋值运算符讲解**（4 分钟）

   ```python
   # 传统写法
   number = number + 1
   # 简化写法
   number += 1  # 等同于上面的写法

   # 其他自赋值运算符
   number -= 1  # number = number - 1
   number *= 2  # number = number * 2
   ```

3. **数据类型转换**（4 分钟）

   ```python
   # 错误示例
   age = input("How old are you? ")
   print(age >= 18)  # TypeError

   # 正确示例
   age = input("How old are you? ")
   age = int(age)  # 类型转换
   print(age >= 18)  # 正常运行
   ```

**教学方法**：演示法 + 对比法
**设计意图**：为 while 循环中的用户交互做好基础准备

#### 教学内容二：求模运算符应用（8 分钟）

**知识点**：求模运算符的概念和实际应用

**教学活动**：

1. **基本概念**（4 分钟）

   ```python
   print(4 % 3)  # 输出：1
   print(5 % 3)  # 输出：2
   print(6 % 3)  # 输出：0
   ```

2. **实际应用**（4 分钟）
   ```python
   # 判断奇偶数
   number = 7
   if number % 2 == 0:
       print("偶数")
   else:
       print("奇数")
   ```

**设计意图**：为后续循环控制中的条件判断提供工具

#### 教学内容三：while 循环基本语法（5 分钟）

**知识点**：while 循环的基本结构和执行流程

**教学活动**：

```python
current_number = 1
while current_number < 3:
    print(current_number)
    current_number += 1  # 重要：更新循环变量
```

**教学方法**：演示法 + 讲授法
**设计意图**：建立 while 循环的基本概念和语法认知

### 三、while 循环简介（35 分钟）

1. **while 循环基本语法**（10 分钟）

   ```python
   current_number = 1
   while current_number < 3:
       print(current_number)
       current_number += 1
   ```

2. **使用条件表达式退出**（8 分钟）

   ```python
   food = ""
   while food != '不点了':
       food = input("你想吃什么外卖？")
       print(f"好的，已添加：{food}")
   ```

3. **使用标志退出**（8 分钟）

   ```python
   talking = True
   while talking:
       you_say = input("你想说什么？")
       if you_say == '睡着了':
           talking = False
   ```

4. **使用 break 退出**（9 分钟）
   ```python
   while True:
       if current_number > 5:
           break
       current_number += 1
       print(current_number)
   ```

### 四、循环控制进阶（25 分钟）

1. **使用 continue 跳过循环**（10 分钟）

   ```python
   current_number = 0
   while True:
       current_number += 1
       if current_number % 2 == 0:
           continue  # 跳过偶数
       print(current_number)
   ```

2. **课堂练习：continue 与 break 结合**（10 分钟）

   - 学生分析代码执行结果
   - 讨论程序逻辑

3. **避免死循环**（5 分钟）
   ```python
   # 错误示例
   x = 1
   while x <= 5:
       print(x)  # 忘记更新x，造成死循环
   ```

### 五、while 循环处理列表和字典（25 分钟）

1. **处理列表案例**（10 分钟）

   ```python
   # 学生签到系统
   student_list = ['xiaoming', 'xiaohong', 'xiaoli']
   in_class = []
   while student_list:
       student = student_list.pop()
       in_class.append(student)
   ```

2. **删除列表中的特定值**（8 分钟）

   ```python
   pets = ['dog', 'cat', 'dog', 'cat', 'rabbit']
   while 'cat' in pets:
       pets.remove('cat')
   ```

3. **处理字典案例**（7 分钟）
   ```python
   # 毕业旅行投票系统
   votes = {}
   while True:
       name = input("请输入你的名字：")
       place = input("你最想去哪里？")
       votes[name] = place
       if input("还有人要投票吗？(yes/no)：") == 'no':
           break
   ```

### 六、小结（5 分钟）

1. input()函数的使用要点
2. while 循环的四种控制方式
3. while 循环与数据结构的结合应用
4. 避免死循环的注意事项

## 参考资料

1. 《Python 编程：从入门到实践》第 7 章
2. Python 官方文档：控制流程语句
3. 在线编程练习平台相关题目

## 作业布置

1. **课后练习**：

   - 下周一前提交超星学习通第七章作业
   - 编写一个简单的菜单驱动程序

2. **预习要求**：
   - 预习第八章函数，思考重点难点
   - 思考如何将重复的代码块封装成函数

---

**教学反思**：
本节课重点在于让学生理解 while 循环的控制逻辑，特别要注意死循环的预防。通过实际案例让学生体会 while 循环在处理用户交互和数据处理中的重要作用。
