# 第六章 使用字典 教案

## 授课章节
第六章 字典

## 教学目的及要求

### 教学目的
通过本章学习，使学生掌握Python字典的基本概念、操作方法和应用场景，能够熟练运用字典解决实际编程问题。

### 教学要求
1. **知识要求**：理解字典的数据结构特点，掌握字典的创建、访问、修改、删除等基本操作
2. **能力要求**：能够选择合适的数据结构（字典vs列表），能够遍历字典，处理嵌套数据结构
3. **素质要求**：培养逻辑思维能力和解决实际问题的编程思维

## 教学重点与难点

### 教学重点
1. 字典的基本概念（键值对结构）
2. 字典的基本操作：创建、访问、添加、修改、删除
3. 字典的遍历方法（keys(), values(), items()）
4. 字典与列表的选择使用

### 教学难点
1. 嵌套数据结构的理解和操作
2. get()方法避免KeyError的使用
3. 字典和列表的适用场景判断

## 教学方法
1. **讲授法**：系统讲解字典的概念和操作方法
2. **演示法**：通过代码演示展示字典的各种操作
3. **讨论法**：引导学生讨论字典vs列表的使用场景
4. **练习法**：随堂练习巩固所学知识

## 教学过程设计

### 一、导入新课（5分钟）
**问题引入**：如何更好地存储和管理有标签的数据？
- 展示用列表存储个人信息的局限性
- 引出字典概念：让数据有"标签"，更清晰

### 二、一个简单的字典（15分钟）
1. **字典的基本概念**
   - 键值对结构
   - 代码示例：
   ```python
   person = {
       'name': '张三',
       'age': 25,
       'city': '北京'
   }
   ```

### 三、使用字典（40分钟）
1. **访问字典中的值**（8分钟）
   ```python
   print(person['name'])  # 输出：张三
   ```

2. **添加键值对**（8分钟）
   ```python
   person['gender'] = '女'
   ```

3. **从空字典开始**（8分钟）
   ```python
   person = {}
   person['name'] = '张三'
   ```

4. **修改字典中的值**（8分钟）
   ```python
   person['city'] = '贵阳'
   ```

5. **删除键值对**（8分钟）
   ```python
   del person['city']
   ```

### 四、遍历字典（25分钟）
1. **遍历所有键值对**（8分钟）
   ```python
   for name, subject in favorite_subjects.items():
       print(f"{name} 喜欢 {subject}。")
   ```

2. **遍历所有键**（8分钟）
   ```python
   for name in favorite_subjects.keys():
       print(name)
   ```

3. **按特定顺序遍历**（9分钟）
   ```python
   for name in sorted(favorite_subjects.keys()):
       print(name)
   ```

### 五、嵌套（20分钟）
1. **字典列表**（7分钟）
   ```python
   students = [student_0, student_1, student_2]
   ```

2. **在字典中存储列表**（7分钟）
   ```python
   student = {
       'name': '小明',
       'hobbies': ['篮球', '画画', '下围棋']
   }
   ```

3. **在字典中存储字典**（6分钟）
   ```python
   family = {
       'dad': {'name': '李大志', 'age': 45}
   }
   ```

### 六、小结（10分钟）
1. 字典基础概念回顾
2. 字典操作方法总结
3. 遍历字典的三种方法
4. 嵌套数据结构应用

## 参考资料
1. 《Python编程：从入门到实践》第6章
2. Python官方文档：字典类型
3. 相关在线编程练习平台

## 作业布置
1. **课后练习**：
   - 超星学习通第六章所有习题，下周一前完成
   - 创建一个学生信息管理小程序，包含增删改查功能

2. **预习要求**：
   - 预习第7章while循环，思考重点难点
   - 思考字典和循环结合的应用场景

---
**教学反思**：
本节课通过循序渐进的方式介绍字典概念，从简单到复杂，注重实践操作。需要特别关注学生对嵌套结构的理解程度，适时调整教学节奏。
