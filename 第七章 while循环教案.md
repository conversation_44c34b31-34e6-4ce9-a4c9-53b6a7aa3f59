# 第七章 while循环 教案

## 授课章节
第七章 用户输入和while循环

## 教学目的及要求

### 教学目的
通过本章学习，使学生掌握用户输入处理和while循环的使用方法，能够编写交互式程序和处理不确定次数的循环问题。

### 教学要求
1. **知识要求**：掌握input()函数的使用，理解while循环的工作原理和控制方法
2. **能力要求**：能够编写交互式程序，合理使用while循环处理列表和字典
3. **素质要求**：培养程序设计的逻辑思维和用户体验意识

## 教学重点与难点

### 教学重点
1. input()函数的使用和数据类型转换
2. while循环的基本语法和执行流程
3. 循环控制：break和continue的使用
4. while循环与列表、字典的结合应用

### 教学难点
1. 避免死循环的程序设计
2. 复杂条件下的循环控制逻辑
3. while循环在实际项目中的应用场景

## 教学方法
1. **讲授法**：系统讲解while循环的概念和语法
2. **演示法**：通过实际代码演示循环的执行过程
3. **实践法**：学生动手编写交互式程序
4. **案例法**：通过实际案例展示while循环的应用

## 教学过程设计

### 一、导入新课（5分钟）
**问题引入**：如何让程序与用户进行交互？如何处理不确定次数的重复操作？
- 回顾for循环的特点（确定次数）
- 引出while循环的必要性（不确定次数）

### 二、input()函数的工作原理（20分钟）
1. **基本用法**（8分钟）
   ```python
   name = input("请输入你的名字：")
   print(f"\n你好，{name}！欢迎来到编程课堂。")
   ```

2. **自赋值运算符**（7分钟）
   ```python
   number = 0
   number += 1  # 等同于 number = number + 1
   ```

3. **获取数值输入**（5分钟）
   ```python
   age = input("How old are you? ")
   age = int(age)  # 类型转换
   print(age >= 18)
   ```

### 三、while循环简介（35分钟）
1. **while循环基本语法**（10分钟）
   ```python
   current_number = 1
   while current_number < 3:
       print(current_number)
       current_number += 1
   ```

2. **使用条件表达式退出**（8分钟）
   ```python
   food = ""
   while food != '不点了':
       food = input("你想吃什么外卖？")
       print(f"好的，已添加：{food}")
   ```

3. **使用标志退出**（8分钟）
   ```python
   talking = True
   while talking:
       you_say = input("你想说什么？")
       if you_say == '睡着了':
           talking = False
   ```

4. **使用break退出**（9分钟）
   ```python
   while True:
       if current_number > 5:
           break
       current_number += 1
       print(current_number)
   ```

### 四、循环控制进阶（25分钟）
1. **使用continue跳过循环**（10分钟）
   ```python
   current_number = 0
   while True:
       current_number += 1
       if current_number % 2 == 0:
           continue  # 跳过偶数
       print(current_number)
   ```

2. **课堂练习：continue与break结合**（10分钟）
   - 学生分析代码执行结果
   - 讨论程序逻辑

3. **避免死循环**（5分钟）
   ```python
   # 错误示例
   x = 1
   while x <= 5:
       print(x)  # 忘记更新x，造成死循环
   ```

### 五、while循环处理列表和字典（25分钟）
1. **处理列表案例**（10分钟）
   ```python
   # 学生签到系统
   student_list = ['xiaoming', 'xiaohong', 'xiaoli']
   in_class = []
   while student_list:
       student = student_list.pop()
       in_class.append(student)
   ```

2. **删除列表中的特定值**（8分钟）
   ```python
   pets = ['dog', 'cat', 'dog', 'cat', 'rabbit']
   while 'cat' in pets:
       pets.remove('cat')
   ```

3. **处理字典案例**（7分钟）
   ```python
   # 毕业旅行投票系统
   votes = {}
   while True:
       name = input("请输入你的名字：")
       place = input("你最想去哪里？")
       votes[name] = place
       if input("还有人要投票吗？(yes/no)：") == 'no':
           break
   ```

### 六、小结（5分钟）
1. input()函数的使用要点
2. while循环的四种控制方式
3. while循环与数据结构的结合应用
4. 避免死循环的注意事项

## 参考资料
1. 《Python编程：从入门到实践》第7章
2. Python官方文档：控制流程语句
3. 在线编程练习平台相关题目

## 作业布置
1. **课后练习**：
   - 下周一前提交超星学习通第七章作业
   - 编写一个简单的菜单驱动程序

2. **预习要求**：
   - 预习第八章函数，思考重点难点
   - 思考如何将重复的代码块封装成函数

---
**教学反思**：
本节课重点在于让学生理解while循环的控制逻辑，特别要注意死循环的预防。通过实际案例让学生体会while循环在处理用户交互和数据处理中的重要作用。
