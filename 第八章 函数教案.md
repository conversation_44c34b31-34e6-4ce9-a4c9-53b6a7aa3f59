# Python 程序设计课程教学设计方案

## 基本信息

- **课程名称**：Python 程序设计
- **授课章节**：第八章 函数（上）
- **授课班级**：计算机科学与技术专业
- **授课时间**：90 分钟（2 学时）
- **授课教师**：冯子晋
- **授课地点**：计算机实验室

## 教学分析

### 学情分析

学生已掌握 Python 基础语法、数据结构（列表、字典）、循环控制等知识，具备一定的编程基础。但对模块化编程思维还需要培养，对代码重用和函数设计的理解需要通过实践来加强。

### 教材分析

本章是 Python 程序设计的核心内容，函数作为程序模块化的基础，是提高代码质量和开发效率的重要工具。内容包括函数定义、参数传递、返回值处理和实际应用，是后续面向对象编程的重要基础。

## 教学目标

### 知识目标

1. 理解函数的概念和作用，掌握函数的定义语法
2. 掌握形参与实参的概念，理解参数传递机制
3. 熟练使用位置实参、关键字实参、默认值参数
4. 掌握函数返回值的设计和处理方法
5. 理解函数与列表等数据结构的结合应用

### 能力目标

1. 能够设计和实现功能明确的函数模块
2. 能够选择合适的参数传递方式
3. 能够处理不同类型的返回值
4. 能够使用函数进行代码重构和优化
5. 能够结合函数处理复杂的数据结构

### 素质目标

1. 培养学生的模块化编程思维和系统设计能力
2. 提高学生的代码重用意识和软件工程素养
3. 培养学生良好的编程习惯和代码规范意识
4. 增强学生解决复杂问题的分解和抽象能力

## 教学重点与难点

### 教学重点

1. 函数的定义语法和调用方法
2. 形参与实参的概念和使用规则
3. 三种参数传递方式的特点和应用
4. 函数返回值的设计原则和处理技巧
5. 函数在代码重构中的实际应用

### 教学难点

1. 参数传递方式的选择和混合使用
2. 复杂返回值的设计和处理
3. 函数接口的设计和优化
4. 函数与数据结构的深度结合应用
5. 代码重构的思路和实践方法

## 教学方法与手段

### 教学方法

1. **讲授法**：系统讲解函数的概念、语法和设计原则
2. **演示法**：通过实际代码演示展示函数的定义、调用和应用过程
3. **案例教学法**：通过实际项目案例展示函数的重要作用
4. **对比教学法**：对比不同参数传递方式的特点和适用场景
5. **重构教学法**：通过代码重构实践展示函数的价值和应用

### 教学手段

1. **多媒体课件**：使用 PPT 展示概念、语法和设计思路
2. **编程环境**：使用 Python IDE 进行实时代码演示和调试
3. **互动平台**：利用课堂互动系统进行实时问答和代码分享
4. **在线资源**：结合超星学习通平台进行课后练习和项目实践

## 教学过程设计

### 第一环节：课程导入（8 分钟）

#### 教学内容

**问题情境创设**：如何避免代码重复？如何让程序更加模块化？

#### 教学活动

1. **问题展示**（4 分钟）

   - 展示重复代码的问题：

   ```python
   # 重复的问候代码
   print("Hello, Alice!")
   print("Hello, Bob!")
   print("Hello, Charlie!")
   ```

   - 分析问题：代码冗余、维护困难、容易出错

2. **解决方案引入**（4 分钟）
   - 引出函数概念：可重复调用的代码块
   - 展示函数的优势：代码重用、模块化设计、易于维护

#### 设计意图

通过具体的代码问题激发学生学习函数的兴趣，建立函数必要性的认知。

### 第二环节：核心知识学习（35 分钟）

#### 教学内容一：函数的基本概念和定义（12 分钟）

**知识点**：函数的概念、作用和基本语法

**教学活动**：

1. **概念讲解**（4 分钟）

   - 函数的定义：执行特定任务的代码块
   - 函数的作用：代码重用、模块化设计、提高可读性

2. **简单函数定义演示**（4 分钟）

   ```python
   def greet_user():
       """显示简单的问候语"""
       print("Hello!")

   greet_user()  # 函数调用
   ```

3. **函数命名和文档字符串**（4 分钟）
   - 函数命名规范：使用描述性名称，遵循 snake_case
   - 文档字符串的作用和写法

**教学方法**：讲授法 + 演示法
**设计意图**：建立函数的基本概念，掌握函数定义的基本语法

#### 教学内容二：形参与实参（10 分钟）

**知识点**：形参与实参的概念和使用

**教学活动**：

1. **概念区分**（4 分钟）

   ```python
   def greet_user(username):  # username是形参
       """显示简单的问候语"""
       print(f"Hello, {username.title()}!")

   greet_user('jesse')  # 'jesse'是实参
   ```

2. **多参数函数**（6 分钟）

   ```python
   def describe_pet(animal_type, pet_name):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")

   describe_pet('cat', 'harry')
   ```

**教学方法**：演示法 + 对比法
**设计意图**：让学生理解参数传递的基本机制

#### 教学内容三：参数传递方式（13 分钟）

**知识点**：位置实参、关键字实参、默认值参数

**教学活动**：

1. **位置实参**（4 分钟）

   ```python
   describe_pet('cat', 'harry')  # 顺序很重要
   describe_pet('harry', 'cat')  # 错误的顺序
   ```

2. **关键字实参**（4 分钟）

   ```python
   describe_pet(animal_type='cat', pet_name='harry')
   describe_pet(pet_name='harry', animal_type='cat')  # 顺序可变
   ```

3. **默认值参数**（5 分钟）

   ```python
   def describe_pet(pet_name, animal_type='dog'):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")

   describe_pet('willie')  # 使用默认值
   describe_pet('harry', 'cat')  # 覆盖默认值
   ```

**教学方法**：演示法 + 对比法
**设计意图**：让学生掌握不同参数传递方式的特点和使用场景

### 三、传递实参（40 分钟）

1. **位置实参**（12 分钟）

   ```python
   def describe_pet(animal_type, pet_name):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")

   describe_pet('cat', 'harry')
   ```

   - 强调参数顺序的重要性
   - 展示顺序错误的后果

2. **关键字实参**（10 分钟）

   ```python
   describe_pet(animal_type='cat', pet_name='harry')
   describe_pet(pet_name='harry', animal_type='cat')  # 顺序可变
   ```

3. **默认值参数**（10 分钟）

   ```python
   def describe_pet(pet_name, animal_type='dog'):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")

   describe_pet('willie')  # 使用默认值
   describe_pet('harry', 'cat')  # 覆盖默认值
   ```

4. **等效的函数调用**（8 分钟）
   - 讨论多种调用方式的等效性
   - 最佳实践建议

### 四、返回值（30 分钟）

1. **返回简单值**（10 分钟）

   ```python
   def get_formatted_name(first_name, last_name):
       """返回标准格式的姓名"""
       full_name = f"{first_name} {last_name}"
       return full_name.title()

   musician = get_formatted_name('jimi', 'hendrix')
   print(musician)
   ```

2. **让实参可选**（10 分钟）

   ```python
   def get_formatted_name(first_name, last_name, middle_name=''):
       """返回标准格式的姓名"""
       if middle_name:
           full_name = f"{first_name} {middle_name} {last_name}"
       else:
           full_name = f"{first_name} {last_name}"
       return full_name.title()
   ```

3. **返回字典**（10 分钟）
   ```python
   def build_person(first_name, last_name, age=None):
       """返回一个字典，包含人的信息"""
       person = {'first': first_name, 'last': last_name}
       if age:
           person['age'] = age
       return person
   ```

### 五、传递列表（20 分钟）

1. **基本列表传递**（8 分钟）

   ```python
   def greet_users(names):
       """向列表中的每个用户发出问候"""
       for name in names:
           msg = f"Hello, {name.title()}!"
           print(msg)

   usernames = ['hannah', 'ty', 'margot']
   greet_users(usernames)
   ```

2. **在函数中修改列表**（12 分钟）
   - 代码重构示例：学生签到系统
   ```python
   def sign_in_students(student_list, in_class):
       """将等待签到的学生逐一签到"""
       while student_list:
           current_student = student_list.pop()
           print(f"{current_student.title()} 正在签到...")
           in_class.append(current_student)
   ```

### 六、课堂练习与讨论（15 分钟）

1. **奶茶订购系统**（10 分钟）

   - 学生完成代码补全练习
   - 结合 while 循环和函数

2. **图书借阅系统**（5 分钟）
   - 学生设计函数处理借书流程

### 七、小结（5 分钟）

1. 函数定义的基本语法
2. 参数传递的三种方式
3. 返回值的设计原则
4. 函数在代码重构中的作用

## 参考资料

1. 《Python 编程：从入门到实践》第 8 章
2. Python 官方文档：函数定义
3. 代码重构相关资料

## 作业布置

1. **课后练习**：

   - 下周一前提交超星学习通第八章（上）作业
   - 设计一个学生成绩管理系统，包含多个函数模块

2. **预习要求**：
   - 预习第八章（下），思考重点难点
   - 了解任意数量参数、模块导入等高级特性

---

**教学反思**：
本节课重点培养学生的模块化编程思维。通过代码重构的实例让学生体会函数的价值。需要特别关注学生对参数传递方式的理解，适时进行练习巩固。
