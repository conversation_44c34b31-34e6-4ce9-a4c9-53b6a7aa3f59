# 第八章 函数 教案

## 授课章节
第八章 函数（上）

## 教学目的及要求

### 教学目的
通过本章学习，使学生掌握Python函数的定义、调用和参数传递，理解函数在程序设计中的重要作用，能够编写可重用的代码模块。

### 教学要求
1. **知识要求**：掌握函数的定义语法、参数类型、返回值机制
2. **能力要求**：能够设计合理的函数接口，处理不同类型的参数和返回值
3. **素质要求**：培养模块化编程思维和代码重用意识

## 教学重点与难点

### 教学重点
1. 函数的定义和调用语法
2. 形参与实参的概念和使用
3. 位置实参、关键字实参、默认值参数
4. 函数返回值的处理
5. 函数与列表的结合使用

### 教学难点
1. 参数传递的多种方式及其适用场景
2. 函数返回值的设计和处理
3. 函数在代码重构中的应用
4. 复杂数据结构作为函数参数的处理

## 教学方法
1. **讲授法**：系统讲解函数的概念和语法规则
2. **演示法**：通过代码演示展示函数的定义和调用过程
3. **对比法**：对比不同参数传递方式的特点
4. **重构法**：通过代码重构展示函数的价值

## 教学过程设计

### 一、导入新课（5分钟）
**问题引入**：如何避免代码重复？如何让程序更加模块化？
- 展示重复代码的问题
- 引出函数的概念：可重复调用的代码块

### 二、定义函数（20分钟）
1. **函数的基本概念**（5分钟）
   - 定义：执行特定任务的代码块
   - 作用：代码重用、模块化设计

2. **简单函数定义**（8分钟）
   ```python
   def greet_user():
       """显示简单的问候语"""
       print("Hello!")
   
   greet_user()  # 函数调用
   ```

3. **实参和形参**（7分钟）
   ```python
   def greet_user(username):  # username是形参
       """显示简单的问候语"""
       print(f"Hello, {username.title()}!")
   
   greet_user('jesse')  # 'jesse'是实参
   ```

### 三、传递实参（40分钟）
1. **位置实参**（12分钟）
   ```python
   def describe_pet(animal_type, pet_name):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")
   
   describe_pet('cat', 'harry')
   ```
   - 强调参数顺序的重要性
   - 展示顺序错误的后果

2. **关键字实参**（10分钟）
   ```python
   describe_pet(animal_type='cat', pet_name='harry')
   describe_pet(pet_name='harry', animal_type='cat')  # 顺序可变
   ```

3. **默认值参数**（10分钟）
   ```python
   def describe_pet(pet_name, animal_type='dog'):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")
   
   describe_pet('willie')  # 使用默认值
   describe_pet('harry', 'cat')  # 覆盖默认值
   ```

4. **等效的函数调用**（8分钟）
   - 讨论多种调用方式的等效性
   - 最佳实践建议

### 四、返回值（30分钟）
1. **返回简单值**（10分钟）
   ```python
   def get_formatted_name(first_name, last_name):
       """返回标准格式的姓名"""
       full_name = f"{first_name} {last_name}"
       return full_name.title()
   
   musician = get_formatted_name('jimi', 'hendrix')
   print(musician)
   ```

2. **让实参可选**（10分钟）
   ```python
   def get_formatted_name(first_name, last_name, middle_name=''):
       """返回标准格式的姓名"""
       if middle_name:
           full_name = f"{first_name} {middle_name} {last_name}"
       else:
           full_name = f"{first_name} {last_name}"
       return full_name.title()
   ```

3. **返回字典**（10分钟）
   ```python
   def build_person(first_name, last_name, age=None):
       """返回一个字典，包含人的信息"""
       person = {'first': first_name, 'last': last_name}
       if age:
           person['age'] = age
       return person
   ```

### 五、传递列表（20分钟）
1. **基本列表传递**（8分钟）
   ```python
   def greet_users(names):
       """向列表中的每个用户发出问候"""
       for name in names:
           msg = f"Hello, {name.title()}!"
           print(msg)
   
   usernames = ['hannah', 'ty', 'margot']
   greet_users(usernames)
   ```

2. **在函数中修改列表**（12分钟）
   - 代码重构示例：学生签到系统
   ```python
   def sign_in_students(student_list, in_class):
       """将等待签到的学生逐一签到"""
       while student_list:
           current_student = student_list.pop()
           print(f"{current_student.title()} 正在签到...")
           in_class.append(current_student)
   ```

### 六、课堂练习与讨论（15分钟）
1. **奶茶订购系统**（10分钟）
   - 学生完成代码补全练习
   - 结合while循环和函数

2. **图书借阅系统**（5分钟）
   - 学生设计函数处理借书流程

### 七、小结（5分钟）
1. 函数定义的基本语法
2. 参数传递的三种方式
3. 返回值的设计原则
4. 函数在代码重构中的作用

## 参考资料
1. 《Python编程：从入门到实践》第8章
2. Python官方文档：函数定义
3. 代码重构相关资料

## 作业布置
1. **课后练习**：
   - 下周一前提交超星学习通第八章（上）作业
   - 设计一个学生成绩管理系统，包含多个函数模块

2. **预习要求**：
   - 预习第八章（下），思考重点难点
   - 了解任意数量参数、模块导入等高级特性

---
**教学反思**：
本节课重点培养学生的模块化编程思维。通过代码重构的实例让学生体会函数的价值。需要特别关注学生对参数传递方式的理解，适时进行练习巩固。
